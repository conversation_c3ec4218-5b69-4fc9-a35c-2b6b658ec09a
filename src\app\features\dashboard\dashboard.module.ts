import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { QuillModule } from 'ngx-quill';
import { DashboardRoutingModule } from './dashboard-routing.module';
import { DashboardComponent } from './dashboard.component';
import { MediaDataComponent } from './pages/media-data/media-data.component';
import { MyPitchesComponent } from './pages/my-pitches/my-pitches.component';

import { SharedModule } from 'src/app/shared/shared.module';
import { AddEventComponent } from './pages/events/add-event/add-event.component';

@NgModule({
  declarations: [
    DashboardComponent,
    MediaDataComponent,
    MyPitchesComponent,
    AddEventComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    DashboardRoutingModule,
    SharedModule,
    QuillModule.forRoot(),
  ],
})
export class DashboardModule {}
