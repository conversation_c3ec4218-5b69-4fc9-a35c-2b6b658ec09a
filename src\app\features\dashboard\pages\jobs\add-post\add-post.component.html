<div class="container mx-auto p-4 md:p-6">
  <div class="flex items-center gap-4 mb-3">
    <button
      (click)="goBack()"
      class="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5 mr-2"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M10 19l-7-7m0 0l7-7m-7 7h18"
        />
      </svg>
    </button>
    <h1 class="text-xl md:text-2xl font-semibold">Post a Job Opportunity</h1>
    <button class="text-gray-500 hover:text-gray-700">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
        />
      </svg>
    </button>
  </div>
  <app-stepper [steps]="steps" [currentStep]="currentStep"></app-stepper>
  <div class="flex justify-end mt-4">
    <button
      *ngIf="currentStep > 1"
      class="bg-gray-300 text-gray-700 px-6 py-2.5 rounded-lg mr-4"
      (click)="currentStep = currentStep - 1"
    >
      Previous
    </button>
    <button
      *ngIf="currentStep < steps.length"
      class="bg-gradient-primary text-white px-6 py-2.5 rounded-lg"
      (click)="currentStep = currentStep + 1"
    >
      Next
    </button>
    <button
      *ngIf="currentStep === steps.length"
      class="bg-gradient-primary text-white px-6 py-2.5 rounded-lg"
    >
      Submit
    </button>
  </div>
  <div class="mt-8">
    <div *ngIf="currentStep === 1">
      <h2 class="text-xl font-semibold mb-4">Details</h2>
      <!-- Details form -->
      <div class="space-y-6 border p-4 rounded-lg">
        <!-- Name -->
        <div>
          <label class="block text-sm font-medium text-[#11181C] mb-1"
            >Job Title</label
          >
          <input
            type="text"
            [(ngModel)]="user.name"
            class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0B013D] focus:border-transparent"
          />
          <label class="block text-sm font-medium text-[#999999] mb-1"
            >Publicly displayed job title. Ensure it is brief and
            informative.</label
          >
        </div>

        <!-- Email -->
        <div>
          <label class="block text-sm font-medium text-[#11181C] mb-1"
            >Posting for (Company)</label
          >
          <input
            type="text"
            [(ngModel)]="user.email"
            class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0B013D] focus:border-transparent"
          />
          <label class="block text-sm font-medium text-[#999999] mb-1"
            >Enter the organization name this job is associated with.</label
          >
        </div>
        <!-- City Selection -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-[#11181C] mb-1">
              Workplace Type
            </label>
            <select
              [(ngModel)]="selectedCity"
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0B013D] focus:border-transparent bg-white"
            >
              <option value="" disabled selected>Select a City</option>
              <option *ngFor="let city of cities" [ngValue]="city">
                {{ city.name }}
              </option>
            </select>
            <label class="block text-sm font-medium text-[#999999] mb-1">
              Specify the type of workplace for this position (e.g., remote,
              hybrid, on-site).
            </label>
          </div>
          <!-- Job Type Selection -->
          <div>
            <label class="block text-sm font-medium text-[#11181C] mb-1">
              Job Type
            </label>
            <select
              [(ngModel)]="selectedCity"
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0B013D] focus:border-transparent bg-white"
            >
              <option value="" disabled selected>Select a City</option>
              <option *ngFor="let city of cities" [ngValue]="city">
                {{ city.name }}
              </option>
            </select>
            <label class="block text-sm font-medium text-[#999999] mb-1">
              Specify the type of employment for this position (e.g., full-time,
              part-time).
            </label>
          </div>
        </div>
        <!-- Role -->
        <div>
          <label class="block text-sm font-medium text-[#11181C] mb-1"
            >Job Location</label
          >
          <input
            type="text"
            [(ngModel)]="user.role"
            class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0B013D] focus:border-transparent"
          />
          <label class="block text-sm font-medium text-[#999999] mb-1"
            >Please provide the location of the company.</label
          >
        </div>

        <!-- Cover Image (Optional) -->
        <div>
          <!-- Label -->
          <label class="block text-sm font-medium text-[#11181C] mb-1">
            Cover Image (Optional)
          </label>

          <!-- File Input (Hidden) -->
          <input
            type="file"
            id="fileInput"
            class="hidden w-[150px]"
            (change)="onFileSelected($event)"
            accept="image/*"
          />

          <!-- Upload Button -->
          <label
            for="fileInput"
            class="cursor-pointer flex items-center justify-center w-full px-4 py-3 border border-dashed border-gray-400 rounded-lg text-gray-600 hover:bg-gray-50 hover:border-[#0B013D] transition"
            *ngIf="!selectedFile"
          >
            <span class="text-sm">Click to upload an image</span>
          </label>

          <!-- Selected File Preview -->
          <div
            *ngIf="selectedFile"
            class="relative mt-3 w-[150px] border border-gray-300 rounded-lg overflow-hidden"
          >
            <!-- Image preview -->
            <img
              *ngIf="previewUrl"
              [src]="previewUrl"
              alt="Selected file"
              class="w-full h-40 object-fill"
            />

            <!-- File name (if not image) -->
            <div *ngIf="!previewUrl" class="p-3 text-sm text-gray-700">
              {{ selectedFile.name }}
            </div>

            <!-- Close button -->
            <button
              (click)="removeFile()"
              class="absolute top-2 right-2 bg-gray-800 text-white rounded-full p-1 hover:bg-red-600 transition"
            >
              ✕
            </button>
          </div>

          <!-- Helper Text -->
          <label class="block text-sm font-medium text-[#999999] mt-2">
            Upload an image or banner that visually represents this job
            opportunity.
          </label>
        </div>
      </div>
    </div>
    <div *ngIf="currentStep === 2">
      <h2 class="text-xl font-semibold mb-4">Description</h2>
      <div class="space-y-6 border p-4 rounded-lg">
        <div>
          <label class="block text-sm font-medium text-[#9FAB18] mb-1">
            ⚠️ Please avoid including company names or promotional content in
            the description. Submissions with such details may be rejected.
          </label>
          <quill-editor
            [(ngModel)]="editorContent"
            [style]="{ height: '320px', width: '100%' }"
            placeholder="Provide a detailed description of the job responsibilities and requirements..."
            theme="snow"
            [modules]="quillModules"
          >
          </quill-editor>
          <!-- <label class="block text-sm font-medium text-[#999999] mb-1 mt-2">
            Use the rich text editor to format your job description with
            headings, lists, and styling.
          </label> -->
        </div>
      </div>
    </div>
    <div *ngIf="currentStep === 3">
      <h2 class="text-xl font-semibold mb-4">Preview</h2>
      <!-- Main Content -->
      <div class="overflow-hidden">
        <div class="border rounded-lg mb-6">
          <!-- cover image -->
          <img
            src="/assets/images/eventsimg.png"
            alt=""
            class="w-full h-56 object-cover rounded-tl-lg rounded-tr-lg"
          />

          <div class="p-6">
            <!-- Job Content -->
            <div>
              <!-- Title and Type -->
              <div class="mb-6">
                <div class="flex items-center gap-2 mb-2">
                  <span class="text-base text-subtitle uppercase"
                    >Journalist request</span
                  >
                </div>
                <h4 class="font-semibold text-gray-900 mb-4">
                  {{ user.name }}
                </h4>
              </div>

              <!-- external link -->
              <div class="flex items-center gap-2 mb-4">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="icon icon-tabler icons-tabler-outline icon-tabler-external-link"
                >
                  <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                  <path
                    d="M12 6h-6a2 2 0 0 0 -2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-6"
                  />
                  <path d="M11 13l9 -9" />
                  <path d="M15 4h5v5" />
                </svg>
                <a
                  href="https://events.theinsurer.com/eumgasummit"
                  target="_blank"
                  >https://events.theinsurer.com/eumgasummit</a
                >
              </div>

              <!-- Tags -->
              <div class="flex flex-wrap gap-2 mb-6">
                <!-- <span *ngFor="let tag of job.tags" class="badge">
                {{ tag }}
              </span> -->
              </div>
            </div>
            <!-- company Section -->
            <div class="">
              <div class="flex flex-col gap-4">
                <!-- Company Info -->
                <div class="flex items-center gap-4 mb-2">
                  <img
                    [src]="user.avatar"
                    [alt]="user.name"
                    class="w-11 h-11 rounded-lg object-cover"
                  />
                  <div>
                    <h4 class="font-medium text-gray-900 truncate">
                      {{ user.name }}
                    </h4>
                    <p class="text-sm text-gray-500 truncate">
                      {{ user.industry }}
                    </p>
                  </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center gap-3">
                  <button
                    class="px-6 py-2.5 bg-gradient-primary text-white rounded-lg transition-colors flex items-center gap-2"
                  >
                    Apply Now
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </button>
                  <button
                    class="p-2.5 hover:text-black text-gray-600 transition-colors"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="icon icon-tabler icons-tabler-outline icon-tabler-bookmark-plus"
                    >
                      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                      <path
                        d="M12 17l-6 4v-14a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v5"
                      />
                      <path d="M16 19h6" />
                      <path d="M19 16v6" />
                    </svg>
                  </button>
                  <button
                    class="p-2.5 hover:text-black text-gray-600 transition-colors"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="icon icon-tabler icons-tabler-outline icon-tabler-share-3"
                    >
                      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                      <path
                        d="M13 4v4c-6.575 1.028 -9.02 6.788 -10 12c-.037 .206 5.384 -5.962 10 -6v4l8 -7l-8 -7z"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="border p-6 rounded-lg">
          <!-- Job Description -->
          <div class="mb-8">
            <h3 class="text-xl font-semibold text-gray-900 mb-4">
              About the Role
            </h3>
            <p class="whitespace-pre-line text-black1 text-base">
              {{ getEditorContent() }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
