<div class="container mx-auto p-4 md:p-6">
  <div>
    <div class="flex items-center gap-4 mb-3">
      <button
        (click)="goBack()"
        class="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 mr-2"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M10 19l-7-7m0 0l7-7m-7 7h18"
          />
        </svg>
      </button>
      <h1 class="text-xl md:text-2xl font-semibold">Post an Event</h1>
      <button class="text-gray-500 hover:text-gray-700">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
      </button>
    </div>
    <app-stepper [steps]="steps" [currentStep]="currentStep"></app-stepper>

    <div class="flex justify-end mt-4">
      <button
        *ngIf="currentStep > 1"
        class="bg-gray-300 text-gray-700 px-6 py-2.5 rounded-lg mr-4"
        (click)="currentStep = currentStep - 1"
      >
        Previous
      </button>
      <button
        *ngIf="currentStep < steps.length"
        class="bg-gradient-primary text-white px-6 py-2.5 rounded-lg"
        (click)="currentStep = currentStep + 1"
      >
        Next
      </button>
      <button
        *ngIf="currentStep === steps.length"
        class="bg-gradient-primary text-white px-6 py-2.5 rounded-lg"
      >
        Submit
      </button>
    </div>
  </div>
  <div class="p-6">
    <div *ngIf="currentStep === 1">
      <h2 class="text-xl font-semibold mb-4">Details</h2>

      <!-- Details form -->
      <div class="space-y-6 border p-4 rounded-lg">
        <!-- Event Title -->
        <div>
          <label class="block text-sm font-medium text-[#11181C] mb-1"
            >Event Title</label
          >
          <input
            type="text"
            [(ngModel)]="user.name"
            class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0B013D] focus:border-transparent"
          />
          <label class="block text-sm font-medium text-[#999999] mb-1"
            >Give your event a clear, engaging name. This will be publicly
            visible in listings and promotional cards.</label
          >
        </div>

        <!-- Event Topic -->
        <div>
          <label class="block text-sm font-medium text-[#11181C] mb-1"
            >Event Topic</label
          >
          <input
            type="text"
            [(ngModel)]="user.eventTopic"
            class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0B013D] focus:border-transparent"
          />
          <label class="block text-sm font-medium text-[#999999] mb-1"
            >State the main subject your event will focus on. This helps
            categorize the event in listings and filters.</label
          >
        </div>
        <!--Event Theme -->
        <div>
          <label class="block text-sm font-medium text-[#11181C] mb-1"
            >Event Theme</label
          >
          <input
            type="text"
            [(ngModel)]="user.eventTheme"
            class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0B013D] focus:border-transparent"
          />
          <label class="block text-sm font-medium text-[#999999] mb-1"
            >Describe the key angle or perspective your event will explore.
            Helps attendees understand the narrative focus.</label
          >
        </div>

        <!-- Posting for (Company) -->
        <div>
          <label class="block text-sm font-medium text-[#11181C] mb-1"
            >Posting for (Company)</label
          >
          <input
            type="text"
            [(ngModel)]="user.role"
            class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0B013D] focus:border-transparent"
          />
          <label class="block text-sm font-medium text-[#999999] mb-1"
            >Specify the organization or brand hosting this event.</label
          >
        </div>

        <!-- Event Banner -->
        <div>
          <!-- Label -->
          <label class="block text-sm font-medium text-[#11181C] mb-1">
            Event Banner
          </label>

          <!-- File Input (Hidden) -->
          <input
            type="file"
            id="fileInput"
            class="hidden w-[150px]"
            (change)="onFileSelected($event)"
            accept="image/*"
          />

          <!-- Upload Button -->
          <label
            for="fileInput"
            class="cursor-pointer flex items-center justify-center w-full px-4 py-3 border border-dashed border-gray-400 rounded-lg text-gray-600 hover:bg-gray-50 hover:border-[#0B013D] transition"
            *ngIf="!selectedFile"
          >
            <span class="text-sm">Click to upload an image</span>
          </label>

          <!-- Selected File Preview -->
          <div
            *ngIf="selectedFile"
            class="relative mt-3 w-[150px] border border-gray-300 rounded-lg overflow-hidden"
          >
            <!-- Image preview -->
            <img
              *ngIf="previewUrl"
              [src]="previewUrl"
              alt="Selected file"
              class="w-full h-32 aspect-square object-fill"
            />

            <!-- File name (if not image) -->
            <div *ngIf="!previewUrl" class="p-3 text-sm text-gray-700">
              {{ selectedFile.name }}
            </div>

            <!-- Close button -->
            <button
              (click)="removeFile()"
              class="absolute top-2 right-2 bg-gray-800 text-white rounded-full p-1 hover:bg-red-600 transition"
            >
              ✕
            </button>
          </div>

          <!-- Helper Text -->
          <label class="block text-sm font-medium text-[#999999] mt-2">
            Upload an image or banner that visually represents this job
            opportunity.
          </label>
        </div>
        <!-- Event Agenda -->
        <div>
          <!-- Label -->
          <label class="block text-sm font-medium text-[#11181C] mb-1">
            Event Agenda
          </label>

          <!-- File Input (Hidden) -->
          <input
            type="file"
            id="fileInput"
            class="hidden w-[150px]"
            (change)="onFileSelected($event)"
            accept="image/*"
          />

          <!-- Upload Button -->
          <label
            for="fileInput"
            class="cursor-pointer flex items-center justify-center w-full px-4 py-3 border border-dashed border-gray-400 rounded-lg text-gray-600 hover:bg-gray-50 hover:border-[#0B013D] transition"
            *ngIf="!selectedFile"
          >
            <span class="text-sm">Click to upload an image</span>
          </label>

          <!-- Selected File Preview -->
          <div
            *ngIf="selectedFile"
            class="relative mt-3 w-[150px] border border-gray-300 rounded-lg overflow-hidden"
          >
            <!-- Image preview -->
            <img
              *ngIf="previewUrl"
              [src]="previewUrl"
              alt="Selected file"
              class="w-full h-32 aspect-square object-fill"
            />

            <!-- File name (if not image) -->
            <div *ngIf="!previewUrl" class="p-3 text-sm text-gray-700">
              {{ selectedFile.name }}
            </div>

            <!-- Close button -->
            <button
              (click)="removeFile()"
              class="absolute top-2 right-2 bg-gray-800 text-white rounded-full p-1 hover:bg-red-600 transition"
            >
              ✕
            </button>
          </div>

          <!-- Helper Text -->
          <label class="block text-sm font-medium text-[#999999] mt-2">
            Upload an image or banner that visually represents this job
            opportunity.
          </label>
        </div>

        <!-- Job Type Selection -->
        <div>
          <label class="block text-sm font-medium text-[#11181C] mb-1">
            Event Agenda
          </label>
          <label class="block text-sm font-medium text-[#999999] mb-1">
            Search and add speakers participating in the event.
          </label>
          <select
            [(ngModel)]="selectedCity"
            class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0B013D] focus:border-transparent bg-white"
          >
            <option value="" disabled selected>Select a City</option>
            <option *ngFor="let city of cities" [ngValue]="city">
              {{ city.name }}
            </option>
          </select>
        </div>
        <!-- Event Description -->

        <div>
          <label class="block text-sm font-medium text-[#11181C] mb-1">
            Description
          </label>
          <label class="block text-sm font-medium text-[#9FAB18] mb-1">
            ⚠️ Please avoid including company names or promotional content in
            the description. Submissions with such details may be rejected.
          </label>
          <quill-editor
            [(ngModel)]="editorContent"
            [style]="{ height: '320px', width: '100%' }"
            placeholder="Provide a detailed description of the job responsibilities and requirements..."
            theme="snow"
            [modules]="quillModules"
          >
          </quill-editor>
          <label class="block text-sm font-medium text-[#999999] mb-1 mt-2">
            Use the rich text editor to format your job description with
            headings, lists, and styling.
          </label>
        </div>
      </div>
    </div>
    <div *ngIf="currentStep === 2">
      <h2 class="text-xl font-semibold mb-4">Location & timing</h2>
      <!-- Location & timing form -->
      <div class="space-y-6 border p-4 rounded-lg">
        <!-- Event Title -->
        <div>
          <label class="block text-sm font-medium text-[#11181C] mb-1"
            >Event Type</label
          >
          <select
            [(ngModel)]="selectedCity"
            class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0B013D] focus:border-transparent bg-white"
          >
            <option value="" disabled selected>Select a City</option>
            <option *ngFor="let city of cities" [ngValue]="city">
              {{ city.name }}
            </option>
          </select>
          <label class="block text-sm font-medium text-[#999999] mb-1"
            >Select whether this is an online or in-person event.</label
          >
        </div>

        <!-- Event Location -->
        <div>
          <label class="block text-sm font-medium text-[#11181C] mb-1"
            >Event Location</label
          >
          <input
            type="text"
            [(ngModel)]="user.email"
            class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0B013D] focus:border-transparent"
          />
          <label class="block text-sm font-medium text-[#999999] mb-1"
            >Enter the physical location where the event will take place.</label
          >
        </div>
        <!-- External event link -->
        <div>
          <label class="block text-sm font-medium text-[#11181C] mb-1"
            >External event link</label
          >
          <input
            type="text"
            [(ngModel)]="user.email"
            class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0B013D] focus:border-transparent"
          />
          <label class="block text-sm font-medium text-[#999999] mb-1"
            >Add a link to the external registration page or event
            website.</label
          >
        </div>

        <!-- Timezone -->
        <div>
          <label class="block text-sm font-medium text-[#11181C] mb-1">
            Timezone
          </label>

          <select
            [(ngModel)]="selectedCity"
            class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0B013D] focus:border-transparent bg-white"
          >
            <option value="" disabled selected>Select a City</option>
            <option *ngFor="let city of cities" [ngValue]="city">
              {{ city.name }}
            </option>
          </select>
          <label class="block text-sm font-medium text-[#999999] mb-1">
            Select the timezone in which the event will occur.
          </label>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!--  Start Date -->
          <div>
            <label class="block text-sm font-medium text-[#11181C] mb-1"
              >Start Date</label
            >
            <input
              type="date"
              [(ngModel)]="user.role"
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0B013D] focus:border-transparent"
            />
            <label class="block text-sm font-medium text-[#999999] mb-1"
              >Choose the starting date of the event.</label
            >
          </div>
          <!-- Posting for (Company) -->
          <div>
            <label class="block text-sm font-medium text-[#11181C] mb-1"
              >Start Time</label
            >
            <input
              type="time"
              [(ngModel)]="user.role"
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0B013D] focus:border-transparent"
            />
            <label class="block text-sm font-medium text-[#999999] mb-1"
              >Set the exact time the event will begin.</label
            >
          </div>
        </div>

        <div>
          <!-- check -->
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!--  Start Date -->
          <div>
            <label class="block text-sm font-medium text-[#11181C] mb-1"
              >End Date</label
            >
            <input
              type="date"
              [(ngModel)]="user.role"
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0B013D] focus:border-transparent"
            />
            <label class="block text-sm font-medium text-[#999999] mb-1"
              >If the event spans multiple days, provide an end date.</label
            >
          </div>
          <!-- Posting for (Company) -->
          <div>
            <label class="block text-sm font-medium text-[#11181C] mb-1"
              >End Time</label
            >
            <input
              type="time"
              [(ngModel)]="user.role"
              class="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0B013D] focus:border-transparent"
            />
            <label class="block text-sm font-medium text-[#999999] mb-1"
              >Add the event’s ending time if known.</label
            >
          </div>
        </div>
      </div>
    </div>

    <div *ngIf="currentStep === 3">
      <h2 class="text-xl font-semibold mb-4">Review</h2>
     
         <!-- Main Content -->
  <div class="max-w-7xl mx-auto border-2 rounded-xl">
    <div class="">
      <!-- Left Column - Event Details -->
      <img
        [src]="event.bannerImage"
        [alt]=""
        class="w-full h-3/4 object-cover rounded-t-xl"
      />
      <div class="p-6">
        <div class="max-w-7xl mx-auto">
          <h1 class="text-2xl font-bold mb-4">{{ event.title }}</h1>
        </div>
        <!-- Event Info -->
        <div class="">
          <!-- Date and Time -->
          <div class="flex items-center gap-2 text-gray-600 mb-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="icon icon-tabler icons-tabler-outline icon-tabler-calendar-week"
            >
              <path stroke="none" d="M0 0h24v24H0z" fill="none" />
              <path
                d="M4 7a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12z"
              />
              <path d="M16 3v4" />
              <path d="M8 3v4" />
              <path d="M4 11h16" />
              <path d="M7 14h.013" />
              <path d="M10.01 14h.005" />
              <path d="M13.01 14h.005" />
              <path d="M16.015 14h.005" />
              <path d="M13.015 17h.005" />
              <path d="M7.01 17h.005" />
              <path d="M10.01 17h.005" />
            </svg>
            <span>{{ event.date }}</span>
          </div>

          <!-- Location -->
          <div class="flex items-start gap-2 text-gray-600 mb-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mt-0.5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
              />
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
              />
            </svg>
            <span>{{ event.location }}</span>
          </div>

          <!-- Event URL -->
          <div class="flex items-center gap-2 text-gray-600 mb-6">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="icon icon-tabler icons-tabler-outline icon-tabler-external-link"
            >
              <path stroke="none" d="M0 0h24v24H0z" fill="none" />
              <path
                d="M12 6h-6a2 2 0 0 0 -2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-6"
              />
              <path d="M11 13l9 -9" />
              <path d="M15 4h5v5" />
            </svg>
            <a [href]="event.eventUrl" target="_blank" class="">{{
              event.eventUrl
            }}</a>
          </div>

          <!-- Right Column - Action Card -->
          <div class="">
            <div class="top-6">
              <!-- Attendees -->
              <div class="flex items-center gap-2 text-gray-600 mb-6">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="icon icon-tabler icons-tabler-outline icon-tabler-users"
                >
                  <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                  <path d="M9 7m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0" />
                  <path d="M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2" />
                  <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                  <path d="M21 21v-2a4 4 0 0 0 -3 -3.85" />
                </svg>
                <span>{{ event.attendees }} attendees</span>
              </div>

              <!-- Speakers -->
              <div class="mb-6">
                <h3 class="text-sm font-medium text-gray-900 mb-3">Speaker</h3>
                <div class="space-y-3">
                  <div
                    *ngFor="let speaker of event.speakers"
                    class="flex items-center gap-3"
                  >
                    <div
                      class="w-10 h-10 rounded-full text-center items-center flex justify-center object-cover bg-subtitlelight"
                    >
                      d
                    </div>
                    <div>
                      <h4 class="text-sm font-medium text-gray-900">
                        Emma Wilsom
                      </h4>
                      <p class="text-xs text-subtitle">CEO, Future Tech</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Tags -->
              <div class="flex flex-wrap gap-2 mb-6">
                <span *ngFor="let tag of event.tags" class="badge">
                  {{ tag }}
                </span>
              </div>

              <!-- Speakers -->
              <div class="mb-6">
                <div class="space-y-3">
                  <div
                    *ngFor="let speaker of event.speakers"
                    class="flex items-center gap-3"
                  >
                    <img
                      [src]="speaker.image"
                      [alt]="speaker.name"
                      class="w-8 h-8 rounded-lg object-cover"
                    />
                    <div>
                      <h4 class="text-sm font-medium text-gray-900">
                        {{ speaker.name }}
                      </h4>
                      <p class="text-xs text-gray-500">{{ speaker.title }}</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="flex items-center gap-2">
                <button
                  (click)="attendEvent()"
                  class="px-4 py-2 bg-gradient-primary text-white rounded-lg hover:bg-[#FF3D7D] transition-colors text-center"
                >
                  Attend Event
                </button>
                <div class="flex items-center gap-1">
                  <button
                    class="p-2 hover:text-black text-gray-600 transition-colors"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="icon icon-tabler icons-tabler-outline icon-tabler-bookmark-plus"
                    >
                      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                      <path
                        d="M12 17l-6 4v-14a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v5"
                      />
                      <path d="M16 19h6" />
                      <path d="M19 16v6" />
                    </svg>
                  </button>
                  <button
                    class="p-2 hover:text-black text-gray-600 transition-colors"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      class="icon icon-tabler icons-tabler-outline icon-tabler-share-3"
                    >
                      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                      <path
                        d="M13 4v4c-6.575 1.028 -9.02 6.788 -10 12c-.037 .206 5.384 -5.962 10 -6v4l8 -7l-8 -7z"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="max-w-7xl mx-auto px-6 py-4 border-2 rounded-xl mt-6 mb-12">
    <div class="">
      <div class="whitespace-pre-line">{{ event.description }}</div>
    </div>
  </div>
    </div>
  </div>
</div>
