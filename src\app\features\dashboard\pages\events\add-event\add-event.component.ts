import { Component } from '@angular/core';
import { Location } from '@angular/common';

interface User {
  eventTitle: string;
  eventTopic: string;
  eventTheme: string;
  postingForCompany: string;
  eventDate: string;
  eventTime: string;
  eventUrl: string;
  eventDescription: string;
  eventBannerImage: string;
  eventSpeakers: string[];
  eventTags: string[];
  eventAttendees: number;
  email: string;
  role: string;
  industry: string;
  industryTags: string[];
  bio: string;
  avatar: string;
}
interface City {
  name: string;
  code: string;
}
@Component({
  selector: 'app-add-event',
  templateUrl: './add-event.component.html',
  styleUrls: ['./add-event.component.css'],
})
export class AddEventComponent {
  steps = ['Details', 'Location & timing', 'Review'];
  currentStep = 1;

  constructor(private location: Location) {}

  nextStep() {
    if (this.currentStep < this.steps.length) {
      this.currentStep++;
    }
  }

  prevStep() {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }
  goBack() {
    this.location.back();
  }

  // for input fields
  user: User = {
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Software Engineer',
    industry: 'Technology',
    industryTags: ['Web Development', 'Angular', 'TypeScript'],
    bio: 'Experienced software engineer with a passion for building great user experiences.',
    avatar: 'assets/images/avatar.png',
  };
  cities: City[] | undefined;

  selectedCity: City | undefined;

  ngOnInit() {
    this.cities = [
      { name: 'New York', code: 'NY' },
      { name: 'Rome', code: 'RM' },
      { name: 'London', code: 'LDN' },
      { name: 'Istanbul', code: 'IST' },
      { name: 'Paris', code: 'PRS' },
    ];
  }
  selectedFile: File | null = null;
  previewUrl: string | null = null;

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.selectedFile = input.files[0];

      // Create image preview
      const reader = new FileReader();
      reader.onload = (e) => (this.previewUrl = e.target?.result as string);
      reader.readAsDataURL(this.selectedFile);
    }
  }

  removeFile(): void {
    this.selectedFile = null;
    this.previewUrl = null;
  }
  editorContent: string = '';
  quillModules = {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'],
      ['blockquote', 'code-block'],
      [{ header: 1 }, { header: 2 }],
      [{ list: 'ordered' }, { list: 'bullet' }],
      [{ script: 'sub' }, { script: 'super' }],
      [{ indent: '-1' }, { indent: '+1' }],
      [{ direction: 'rtl' }],
      [{ size: ['small', false, 'large', 'huge'] }],
      [{ header: [1, 2, 3, 4, 5, 6, false] }],
      [{ color: [] }, { background: [] }],
      [{ font: [] }],
      [{ align: [] }],
      ['clean'],
      ['link'],
    ],
  };
}
