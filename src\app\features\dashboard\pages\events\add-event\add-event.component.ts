import { Component } from '@angular/core';
import { Location } from '@angular/common';

// Event Details Interface
interface EventDetails {
  eventTitle: string;
  eventTopic: string;
  eventTheme: string;
  postingForCompany: string;
  eventBanner: File | null;
  eventAgenda: File | null;
  selectedSpeakers: any[];
  description: string;
}

// Location & Timing Interface
interface LocationTiming {
  eventType: string;
  eventLocation: string;
  externalEventLink: string;
  timezone: string;
  startDate: string;
  startTime: string;
  endDate: string;
  endTime: string;
}

// Speaker Interface
interface Speaker {
  id: string;
  name: string;
  title: string;
  image: string;
}

// City Interface
interface City {
  name: string;
  code: string;
}

// Event Type Interface
interface EventType {
  name: string;
  value: string;
}

// Timezone Interface
interface Timezone {
  name: string;
  value: string;
}
@Component({
  selector: 'app-add-event',
  templateUrl: './add-event.component.html',
  styleUrls: ['./add-event.component.css'],
})
export class AddEventComponent {
  steps = ['Details', 'Location & timing', 'Review'];
  currentStep = 1;

  constructor(private location: Location) {}

  nextStep() {
    if (this.currentStep < this.steps.length) {
      this.currentStep++;
    }
  }

  prevStep() {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }
  goBack() {
    this.location.back();
  }

  // Event Details Data
  eventDetails: EventDetails = {
    eventTitle: '',
    eventTopic: '',
    eventTheme: '',
    postingForCompany: '',
    eventBanner: null,
    eventAgenda: null,
    selectedSpeakers: [],
    description: '',
  };

  // Location & Timing Data
  locationTiming: LocationTiming = {
    eventType: '',
    eventLocation: '',
    externalEventLink: '',
    timezone: '',
    startDate: '',
    startTime: '',
    endDate: '',
    endTime: '',
  };

  // Dropdown Options
  cities: City[] = [];
  eventTypes: EventType[] = [];
  timezones: Timezone[] = [];
  availableSpeakers: Speaker[] = [];

  selectedCity: City | undefined;

  ngOnInit() {
    this.cities = [
      { name: 'New York', code: 'NY' },
      { name: 'Rome', code: 'RM' },
      { name: 'London', code: 'LDN' },
      { name: 'Istanbul', code: 'IST' },
      { name: 'Paris', code: 'PRS' },
    ];

    this.eventTypes = [
      { name: 'Online Event', value: 'online' },
      { name: 'In-Person Event', value: 'in-person' },
      { name: 'Hybrid Event', value: 'hybrid' },
    ];

    this.timezones = [
      { name: 'UTC-05:00 (Eastern Time)', value: 'America/New_York' },
      { name: 'UTC-06:00 (Central Time)', value: 'America/Chicago' },
      { name: 'UTC-07:00 (Mountain Time)', value: 'America/Denver' },
      { name: 'UTC-08:00 (Pacific Time)', value: 'America/Los_Angeles' },
      { name: 'UTC+00:00 (GMT)', value: 'Europe/London' },
      { name: 'UTC+01:00 (CET)', value: 'Europe/Paris' },
    ];

    this.availableSpeakers = [
      {
        id: '1',
        name: 'Emma Wilson',
        title: 'CEO, Future Tech',
        image: 'assets/images/speaker1.jpg',
      },
      {
        id: '2',
        name: 'John Smith',
        title: 'CTO, Innovation Labs',
        image: 'assets/images/speaker2.jpg',
      },
      {
        id: '3',
        name: 'Sarah Johnson',
        title: 'VP Engineering, TechCorp',
        image: 'assets/images/speaker3.jpg',
      },
    ];
  }
  // File handling for banner
  selectedBannerFile: File | null = null;
  bannerPreviewUrl: string | null = null;

  // File handling for agenda
  selectedAgendaFile: File | null = null;
  agendaPreviewUrl: string | null = null;

  onBannerFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.selectedBannerFile = input.files[0];
      this.eventDetails.eventBanner = this.selectedBannerFile;

      // Create image preview
      const reader = new FileReader();
      reader.onload = (e) =>
        (this.bannerPreviewUrl = e.target?.result as string);
      reader.readAsDataURL(this.selectedBannerFile);
    }
  }

  removeBannerFile(): void {
    this.selectedBannerFile = null;
    this.bannerPreviewUrl = null;
    this.eventDetails.eventBanner = null;
  }

  onAgendaFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.selectedAgendaFile = input.files[0];
      this.eventDetails.eventAgenda = this.selectedAgendaFile;

      // Create image preview
      const reader = new FileReader();
      reader.onload = (e) =>
        (this.agendaPreviewUrl = e.target?.result as string);
      reader.readAsDataURL(this.selectedAgendaFile);
    }
  }

  removeAgendaFile(): void {
    this.selectedAgendaFile = null;
    this.agendaPreviewUrl = null;
    this.eventDetails.eventAgenda = null;
  }
  quillModules = {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'],
      ['blockquote', 'code-block'],
      [{ header: 1 }, { header: 2 }],
      [{ list: 'ordered' }, { list: 'bullet' }],
      [{ script: 'sub' }, { script: 'super' }],
      [{ indent: '-1' }, { indent: '+1' }],
      [{ direction: 'rtl' }],
      [{ size: ['small', false, 'large', 'huge'] }],
      [{ header: [1, 2, 3, 4, 5, 6, false] }],
      [{ color: [] }, { background: [] }],
      [{ font: [] }],
      [{ align: [] }],
      ['clean'],
      ['link'],
    ],
  };

  // Get event data for review
  get event() {
    return {
      title: this.eventDetails.eventTitle,
      topic: this.eventDetails.eventTopic,
      theme: this.eventDetails.eventTheme,
      company: this.eventDetails.postingForCompany,
      bannerImage:
        this.bannerPreviewUrl || 'assets/images/default-event-banner.jpg',
      description: this.eventDetails.description,
      eventType: this.locationTiming.eventType,
      location: this.locationTiming.eventLocation,
      eventUrl: this.locationTiming.externalEventLink,
      timezone: this.locationTiming.timezone,
      date: this.formatEventDate(),
      startDate: this.locationTiming.startDate,
      startTime: this.locationTiming.startTime,
      endDate: this.locationTiming.endDate,
      endTime: this.locationTiming.endTime,
      speakers: this.eventDetails.selectedSpeakers,
      attendees: 0, // This would be calculated based on registrations
      tags: this.getEventTags(),
    };
  }

  private formatEventDate(): string {
    if (this.locationTiming.startDate && this.locationTiming.startTime) {
      const date = new Date(
        this.locationTiming.startDate + 'T' + this.locationTiming.startTime
      );
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    }
    return '';
  }

  private getEventTags(): string[] {
    const tags = [];
    if (this.eventDetails.eventTopic) tags.push(this.eventDetails.eventTopic);
    if (this.eventDetails.eventTheme) tags.push(this.eventDetails.eventTheme);
    if (this.locationTiming.eventType) tags.push(this.locationTiming.eventType);
    return tags;
  }

  attendEvent(): void {
    // Handle event attendance logic
    console.log('Attending event:', this.event);
  }
}
